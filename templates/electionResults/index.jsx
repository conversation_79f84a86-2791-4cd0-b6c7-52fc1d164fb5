import React, { useEffect } from 'react';
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit';
import './style.css';

const ElectionResults = () => {
  const {
    candidate1Name = "BIDEN",
    candidate1Party = "DEMOCRAT",
    candidate1Votes = 253,
    candidate1Color = "#1f5582",
    candidate1Image = "",
    candidate2Name = "TRUMP",
    candidate2Party = "REPUBLICAN", 
    candidate2Votes = 214,
    candidate2Color = "#c41e3a",
    candidate2Image = "",
    electionTitle = "US Election 2020",
    electionSubtitle = "Electoral College",
    statesReported = "44 of 50 states",
    winThreshold = 270,
    totalVotes = 538
  } = useCasparData();

  // Calculate progress bar percentages
  const candidate1Percentage = (candidate1Votes / totalVotes) * 100;
  const candidate2Percentage = (candidate2Votes / totalVotes) * 100;
  const thresholdPercentage = (winThreshold / totalVotes) * 100;

  return (
    <GsapTimeline
      onPlay={(timeline) => {
        // Animate the main container sliding up from bottom
        timeline.from('.election-container', { y: 400, duration: 0.8, ease: "power2.out" });
        
        // Animate candidate portraits scaling in
        timeline.from('.candidate-portrait', { scale: 0, duration: 0.5, stagger: 0.2 }, "-=0.4");
        
        // Animate vote counts counting up
        timeline.from('.vote-count', { scale: 0, duration: 0.4, stagger: 0.1 }, "-=0.3");
        
        // Animate progress bars filling
        timeline.from('.progress-fill', { scaleX: 0, duration: 0.8, stagger: 0.1, transformOrigin: "left" }, "-=0.2");
        
        // Animate threshold marker
        timeline.from('.threshold-marker', { scale: 0, duration: 0.3 }, "-=0.2");
        
        // Animate states reported badge
        timeline.from('.states-reported', { scale: 0, duration: 0.3 }, "-=0.1");
      }}
      onStop={(timeline) => {
        // Slide everything down when stopping
        timeline.to('.election-container', { y: 400, duration: 0.5, ease: "power2.in" });
      }}
    >
      <div className="election-container">
        {/* Header */}
        <div className="election-header">
          <div className="election-flag">🇺🇸</div>
          <div className="election-titles">
            <div className="election-title">{electionTitle}</div>
            <div className="election-subtitle">{electionSubtitle}</div>
          </div>
        </div>

        {/* Main Content */}
        <div className="election-main">
          {/* Candidate 1 */}
          <div className="candidate candidate-left">
            <div className="candidate-portrait" style={{ borderColor: candidate1Color }}>
              {candidate1Image ? (
                <img src={candidate1Image} alt={candidate1Name} />
              ) : (
                <div className="portrait-placeholder" style={{ backgroundColor: candidate1Color }}>
                  {candidate1Name.charAt(0)}
                </div>
              )}
            </div>
            <div className="candidate-info">
              <div className="candidate-name" style={{ color: candidate1Color }}>
                {candidate1Name}
              </div>
              <div className="candidate-party" style={{ color: candidate1Color }}>
                {candidate1Party}
              </div>
            </div>
            <div className="vote-count" style={{ backgroundColor: candidate1Color }}>
              {candidate1Votes}
            </div>
          </div>

          {/* States Reported Badge */}
          <div className="states-reported">
            {statesReported}
          </div>

          {/* Candidate 2 */}
          <div className="candidate candidate-right">
            <div className="candidate-portrait" style={{ borderColor: candidate2Color }}>
              {candidate2Image ? (
                <img src={candidate2Image} alt={candidate2Name} />
              ) : (
                <div className="portrait-placeholder" style={{ backgroundColor: candidate2Color }}>
                  {candidate2Name.charAt(0)}
                </div>
              )}
            </div>
            <div className="candidate-info">
              <div className="candidate-name" style={{ color: candidate2Color }}>
                {candidate2Name}
              </div>
              <div className="candidate-party" style={{ color: candidate2Color }}>
                {candidate2Party}
              </div>
            </div>
            <div className="vote-count" style={{ backgroundColor: candidate2Color }}>
              {candidate2Votes}
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="progress-container">
          <div className="progress-bar">
            <div 
              className="progress-fill candidate1-fill" 
              style={{ 
                backgroundColor: candidate1Color,
                width: `${candidate1Percentage}%`
              }}
            ></div>
            <div 
              className="progress-fill candidate2-fill" 
              style={{ 
                backgroundColor: candidate2Color,
                width: `${candidate2Percentage}%`,
                left: `${candidate1Percentage}%`
              }}
            ></div>
          </div>
          
          {/* Threshold Marker */}
          <div 
            className="threshold-marker" 
            style={{ left: `${thresholdPercentage}%` }}
          >
            <div className="threshold-line"></div>
            <div className="threshold-label">{winThreshold} to win</div>
          </div>
        </div>
      </div>
    </GsapTimeline>
  );
};

render(ElectionResults);
