/* Election Results Container */
.election-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 40px 60px;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.2);
  font-family: 'Arial', sans-serif;
}

/* Header Section */
.election-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.election-flag {
  font-size: 48px;
  margin-right: 20px;
}

.election-titles {
  flex: 1;
}

.election-title {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.election-subtitle {
  font-size: 48px;
  font-weight: 900;
  color: #34495e;
  letter-spacing: 2px;
}

/* Main Content */
.election-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
}

/* Candidate Sections */
.candidate {
  display: flex;
  align-items: center;
  flex: 1;
}

.candidate-left {
  justify-content: flex-start;
}

.candidate-right {
  justify-content: flex-end;
  flex-direction: row-reverse;
}

.candidate-portrait {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 6px solid;
  overflow: hidden;
  margin: 0 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.candidate-portrait img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.portrait-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 48px;
  font-weight: bold;
}

.candidate-info {
  text-align: center;
  margin: 0 20px;
}

.candidate-right .candidate-info {
  text-align: center;
}

.candidate-name {
  font-size: 64px;
  font-weight: 900;
  letter-spacing: 3px;
  margin-bottom: 5px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.candidate-party {
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 1px;
}

.vote-count {
  font-size: 96px;
  font-weight: 900;
  color: white;
  padding: 20px 40px;
  border-radius: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  min-width: 200px;
  text-align: center;
}

/* States Reported Badge */
.states-reported {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #2c3e50;
  color: white;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 20px;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

/* Progress Bar */
.progress-container {
  position: relative;
  margin-top: 20px;
}

.progress-bar {
  height: 40px;
  background: #dee2e6;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  position: absolute;
  top: 0;
  transition: width 0.3s ease;
}

.candidate1-fill {
  left: 0;
  border-radius: 20px 0 0 20px;
}

.candidate2-fill {
  border-radius: 0 20px 20px 0;
}

/* Threshold Marker */
.threshold-marker {
  position: absolute;
  top: -15px;
  transform: translateX(-50%);
  z-index: 5;
}

.threshold-line {
  width: 3px;
  height: 70px;
  background: #2c3e50;
  margin: 0 auto;
  position: relative;
}

.threshold-line::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -5px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #2c3e50;
}

.threshold-label {
  background: #2c3e50;
  color: white;
  padding: 8px 16px;
  border-radius: 15px;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  margin-top: 5px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 1600px) {
  .candidate-name {
    font-size: 48px;
  }
  
  .vote-count {
    font-size: 72px;
    padding: 15px 30px;
  }
  
  .election-subtitle {
    font-size: 36px;
  }
}
